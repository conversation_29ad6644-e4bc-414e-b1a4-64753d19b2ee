import 'package:hive_flutter/hive_flutter.dart';
import '../models/invoice.dart';

class HiveMigration {
  /// Migrate existing invoice data to include return fields
  static Future<void> migrateInvoiceData() async {
    try {
      // Check if invoices box exists and has data
      if (Hive.isBoxOpen('invoices')) {
        final invoicesBox = Hive.box<Invoice>('invoices');

        // Check if migration is needed by looking for invoices without return fields
        bool needsMigration = false;

        for (final invoice in invoicesBox.values) {
          // Check if any items need migration (this will be handled by default values)
          // Check if invoice needs migration
          try {
            // Try to access the new fields - if they throw, we need migration
            final _ = invoice.totalReturnedAmount;
            final __ = invoice.hasReturns;
          } catch (e) {
            needsMigration = true;
            break;
          }
        }

        if (needsMigration) {
          print('Migrating invoice data...');

          // Create a list to store migrated invoices
          final migratedInvoices = <Invoice>[];

          for (final invoice in invoicesBox.values) {
            // Create a new invoice with return fields initialized
            final migratedInvoice = Invoice(
              id: invoice.id,
              customer: invoice.customer,
              items: invoice.items.map((item) {
                // Create new InvoiceItem with return fields
                return InvoiceItem(
                  product: item.product,
                  quantity: item.quantity,
                  totalPrice: item.totalPrice,
                  returnedQuantity: 0, // Default value
                  returnedAmount: 0.0, // Default value
                );
              }).toList(),
              date: invoice.date,
              totalAmount: invoice.totalAmount,
              paidAmount: invoice.paidAmount,
              isPaid: invoice.isPaid,
              totalReturnedAmount: 0.0, // Default value
              lastReturnDate: null, // Default value
              hasReturns: false, // Default value
            );

            migratedInvoices.add(migratedInvoice);
          }

          // Clear the box and add migrated data
          await invoicesBox.clear();
          await invoicesBox.addAll(migratedInvoices);

          print('Invoice data migration completed successfully.');
        } else {
          print('No invoice data migration needed.');
        }
      }
    } catch (e) {
      print('Error during invoice data migration: $e');
      // Don't rethrow - let the app continue even if migration fails
    }
  }

  /// Clear all Hive data (use with caution - for development/testing only)
  static Future<void> clearAllData() async {
    try {
      // Close all boxes first
      await Hive.close();

      // Delete all Hive files
      await Hive.deleteFromDisk();

      print('All Hive data cleared.');
    } catch (e) {
      print('Error clearing Hive data: $e');
    }
  }

  /// Emergency fix for type casting errors - clears only invoices data
  static Future<void> clearInvoicesData() async {
    try {
      if (Hive.isBoxOpen('invoices')) {
        await Hive.box('invoices').clear();
        print('Invoices data cleared.');
      }
    } catch (e) {
      print('Error clearing invoices data: $e');
    }
  }
}
