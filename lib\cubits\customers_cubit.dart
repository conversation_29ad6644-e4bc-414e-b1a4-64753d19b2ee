import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import '../models/customer.dart';
import '../models/invoice.dart';
import '../models/payment_record.dart';

// Events
abstract class CustomersEvent extends Equatable {
  const CustomersEvent();

  @override
  List<Object> get props => [];
}

class LoadCustomers extends CustomersEvent {}

class AddCustomer extends CustomersEvent {
  final Customer customer;

  const AddCustomer(this.customer);

  @override
  List<Object> get props => [customer];
}

class UpdateCustomer extends CustomersEvent {
  final Customer customer;

  const UpdateCustomer(this.customer);

  @override
  List<Object> get props => [customer];
}

class DeleteCustomer extends CustomersEvent {
  final Customer customer;

  const DeleteCustomer(this.customer);

  @override
  List<Object> get props => [customer];
}

// State
class CustomersState extends Equatable {
  final List<Customer> customers;

  const CustomersState({
    this.customers = const [],
  });

  CustomersState copyWith({
    List<Customer>? customers,
  }) {
    return CustomersState(
      customers: customers ?? this.customers,
    );
  }

  @override
  List<Object> get props => [customers];
}

// Cubit
class CustomersCubit extends Cubit<CustomersState> {
  final Box<Customer> _customersBox;
  Box<Invoice>? _invoicesBox;
  Box<PaymentRecord>? _paymentRecordsBox;
  dynamic _invoicesCubit; // Using dynamic to avoid circular dependency

  CustomersCubit(this._customersBox) : super(const CustomersState()) {
    loadCustomers();
  }

  // Set the invoices box reference to calculate total paid
  void setInvoicesBox(Box<Invoice> invoicesBox) {
    _invoicesBox = invoicesBox;
    updateCustomerTotalPaid();
  }

  // Set the invoices cubit reference
  void setInvoicesCubit(dynamic invoicesCubit) {
    _invoicesCubit = invoicesCubit;
  }

  // Set the payment records box reference
  void setPaymentRecordsBox(Box<PaymentRecord> paymentRecordsBox) {
    _paymentRecordsBox = paymentRecordsBox;
  }

  void loadCustomers() {
    final customers = _customersBox.values.toList();
    customers.sort((a, b) => a.name.compareTo(b.name));
    emit(CustomersState(customers: customers));
  }

  Future<void> addCustomer(Customer customer) async {
    await _customersBox.add(customer);
    loadCustomers();
  }

  Future<void> updateCustomer(Customer customer) async {
    await customer.save();
    loadCustomers();
  }

  Future<void> deleteCustomer(Customer customer) async {
    try {
      if (_invoicesBox != null && _invoicesCubit != null) {
        // Find all invoices associated with this customer
        final customerInvoices = _invoicesBox!.values
            .where((invoice) => invoice.customer.name == customer.name)
            .toList();

        // Delete each invoice WITHOUT restoring product quantities
        // Products were actually sold and consumed, so quantities should not be returned
        for (final invoice in customerInvoices) {
          try {
            await _invoicesCubit
                .deleteInvoiceWithoutRestockingProducts(invoice);
          } catch (e) {
            // Continue with other invoices even if one fails
            continue;
          }
        }
      }

      // Delete the customer
      if (customer.isInBox) {
        await customer.delete();
      }
      loadCustomers();
    } catch (e) {
      // If there's an error, still try to reload data
      loadCustomers();
      rethrow;
    }
  }

  // Calculate and update the total paid and remaining balance for each customer based on their invoices and payment records
  void updateCustomerTotalPaid() {
    if (_invoicesBox == null) return;

    final invoices = _invoicesBox!.values.toList();
    final customerTotalInvoiceAmountMap = <String, double>{};
    final customerTotalPaidAmountMap = <String, double>{};

    // Calculate total invoice amount for each customer
    for (final invoice in invoices) {
      final customerName = invoice.customer.name;

      // Sum total invoice amount
      customerTotalInvoiceAmountMap[customerName] =
          (customerTotalInvoiceAmountMap[customerName] ?? 0.0) +
              invoice.totalAmount;
    }

    // Calculate total paid amount from payment records if available
    if (_paymentRecordsBox != null) {
      final paymentRecords = _paymentRecordsBox!.values.toList();
      for (final paymentRecord in paymentRecords) {
        final customerName = paymentRecord.customerId;

        // Sum all payment amounts including excess payments
        customerTotalPaidAmountMap[customerName] =
            (customerTotalPaidAmountMap[customerName] ?? 0.0) +
                paymentRecord.amount;
      }
    } else {
      // Fallback to invoice paid amounts if payment records are not available
      for (final invoice in invoices) {
        final customerName = invoice.customer.name;
        customerTotalPaidAmountMap[customerName] =
            (customerTotalPaidAmountMap[customerName] ?? 0.0) +
                (invoice.paidAmount ?? 0.0);
      }
    }

    // Update customer objects
    for (final customer in _customersBox.values) {
      final totalInvoiceAmount =
          customerTotalInvoiceAmountMap[customer.name] ?? 0.0;
      final totalPaidAmount = customerTotalPaidAmountMap[customer.name] ?? 0.0;

      customer.totalPaid = totalPaidAmount;
      // Negative remaining balance means customer has credit (paid more than owed)
      customer.remainingBalance = totalInvoiceAmount - totalPaidAmount;

      customer.save();
    }

    loadCustomers();
  }
}
