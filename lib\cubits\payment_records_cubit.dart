import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/payment_record.dart';

class PaymentRecordsCubit extends Cubit<List<PaymentRecord>> {
  final Box<PaymentRecord> _paymentRecordsBox;

  PaymentRecordsCubit(this._paymentRecordsBox)
      : super(_paymentRecordsBox.values.toList());

  // Add a new payment record
  Future<void> addPaymentRecord(PaymentRecord paymentRecord) async {
    try {
      await _paymentRecordsBox.add(paymentRecord);
      emit(_paymentRecordsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Get all payment records for a specific customer
  List<PaymentRecord> getPaymentRecordsForCustomer(String customerId) {
    return _paymentRecordsBox.values
        .where((record) => record.customerId == customerId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get all payment records for a specific invoice
  List<PaymentRecord> getPaymentRecordsForInvoice(String invoiceId) {
    return _paymentRecordsBox.values
        .where((record) => record.invoiceId == invoiceId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get payment records within a date range
  List<PaymentRecord> getPaymentRecordsInDateRange(
      DateTime startDate, DateTime endDate) {
    return _paymentRecordsBox.values
        .where((record) =>
            record.timestamp.isAfter(startDate) &&
            record.timestamp.isBefore(endDate))
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first
  }

  // Get total payment amount for a customer
  double getTotalPaymentAmountForCustomer(String customerId) {
    return _paymentRecordsBox.values
        .where((record) => record.customerId == customerId)
        .fold(0.0, (sum, record) => sum + record.amount);
  }

  // Get total payment amount for an invoice
  double getTotalPaymentAmountForInvoice(String invoiceId) {
    return _paymentRecordsBox.values
        .where((record) => record.invoiceId == invoiceId)
        .fold(0.0, (sum, record) => sum + record.amount);
  }

  // Delete a payment record
  Future<void> deletePaymentRecord(PaymentRecord paymentRecord) async {
    try {
      await paymentRecord.delete();
      emit(_paymentRecordsBox.values.toList());
    } catch (e) {
      // Handle error
      rethrow;
    }
  }

  // Load all payment records
  void loadPaymentRecords() {
    emit(_paymentRecordsBox.values.toList());
  }
}
