import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/return.dart';
import '../models/payment_record.dart';
import 'cashbox_cubit.dart';
import 'products_cubit.dart';
import 'customers_cubit.dart';
import 'returns_cubit.dart';
import 'payment_records_cubit.dart';

// Events
abstract class InvoicesEvent extends Equatable {
  const InvoicesEvent();

  @override
  List<Object> get props => [];
}

class LoadInvoices extends InvoicesEvent {}

class AddInvoice extends InvoicesEvent {
  final Invoice invoice;

  const AddInvoice(this.invoice);

  @override
  List<Object> get props => [invoice];
}

class DeleteInvoice extends InvoicesEvent {
  final Invoice invoice;

  const DeleteInvoice(this.invoice);

  @override
  List<Object> get props => [invoice];
}

// State
class InvoicesState extends Equatable {
  final List<Invoice> invoices;
  final Map<String, List<Invoice>> customerInvoices;

  const InvoicesState({
    this.invoices = const [],
    this.customerInvoices = const {},
  });

  InvoicesState copyWith({
    List<Invoice>? invoices,
    Map<String, List<Invoice>>? customerInvoices,
  }) {
    return InvoicesState(
      invoices: invoices ?? this.invoices,
      customerInvoices: customerInvoices ?? this.customerInvoices,
    );
  }

  @override
  List<Object> get props => [invoices, customerInvoices];
}

// Cubit
class InvoicesCubit extends Cubit<InvoicesState> {
  final Box<Invoice> _invoicesBox;
  final Box<Product> _productsBox;
  CashboxCubit? _cashboxCubit;
  ProductsCubit? _productsCubit;
  CustomersCubit? _customersCubit;
  ReturnsCubit? _returnsCubit;
  PaymentRecordsCubit? _paymentRecordsCubit;

  InvoicesCubit(this._invoicesBox, this._productsBox)
      : super(const InvoicesState()) {
    loadInvoices();
  }

  // Set the cashbox cubit reference
  void setCashboxCubit(CashboxCubit cashboxCubit) {
    _cashboxCubit = cashboxCubit;
  }

  // Set the products cubit reference
  void setProductsCubit(ProductsCubit productsCubit) {
    _productsCubit = productsCubit;
  }

  // Set the customers cubit reference
  void setCustomersCubit(CustomersCubit customersCubit) {
    _customersCubit = customersCubit;
  }

  // Set the returns cubit reference
  void setReturnsCubit(ReturnsCubit returnsCubit) {
    _returnsCubit = returnsCubit;
  }

  // Set the payment records cubit reference
  void setPaymentRecordsCubit(PaymentRecordsCubit paymentRecordsCubit) {
    _paymentRecordsCubit = paymentRecordsCubit;
  }

  void loadInvoices() {
    final invoices = _invoicesBox.values.toList();
    final customerInvoices = <String, List<Invoice>>{};

    for (final invoice in invoices) {
      customerInvoices
          .putIfAbsent(invoice.customer.name, () => [])
          .add(invoice);
    }

    emit(InvoicesState(
      invoices: invoices,
      customerInvoices: customerInvoices,
    ));
  }

  Future<void> addInvoice(Invoice invoice) async {
    // Update product quantities
    for (final item in invoice.items) {
      final product = item.product;
      product.quantity -= item.quantity;
      await product.save();
    }

    await _invoicesBox.add(invoice);
    loadInvoices();

    // Update cashbox data
    _cashboxCubit?.updateCashbox();

    // Update products data
    _productsCubit?.updateProducts();

    // Update customer total paid
    _customersCubit?.updateCustomerTotalPaid();
  }

  /// Delete invoice and restore product quantities to inventory
  /// Used for manual invoice deletion (corrections, mistakes, etc.)
  Future<void> deleteInvoice(Invoice invoice) async {
    try {
      // Restore product quantities - need to find the actual product in the box
      for (final item in invoice.items) {
        // Get the product from the box by its key to ensure it's a valid box object
        final productInBox = _productsBox.values.firstWhere(
          (p) => p.category == item.product.category,
          orElse: () => item.product,
        );

        // Update the quantity
        productInBox.quantity += item.quantity;

        // Only save if the product is in a box
        if (productInBox.isInBox) {
          await productInBox.save();
        }
      }

      // Delete the invoice if it's in a box
      if (invoice.isInBox) {
        await invoice.delete();
      }

      // Reload invoices to update the UI
      loadInvoices();

      // Update cashbox data immediately
      if (_cashboxCubit != null) {
        _cashboxCubit!.loadCashboxData();
      }

      // Update products data
      if (_productsCubit != null) {
        _productsCubit!.loadProducts();
      }

      // Update customer total paid
      if (_customersCubit != null) {
        _customersCubit!.updateCustomerTotalPaid();
      }
    } catch (e) {
      // Error handling without print
      // Continue with updates even if there was an error
      loadInvoices();
      _cashboxCubit?.loadCashboxData();
      _productsCubit?.loadProducts();
      _customersCubit?.updateCustomerTotalPaid();
    }
  }

  /// Delete invoice without restoring product quantities
  /// Used when deleting customer accounts where products were actually consumed
  Future<void> deleteInvoiceWithoutRestockingProducts(Invoice invoice) async {
    try {
      // Delete the invoice if it's in a box WITHOUT restoring product quantities
      if (invoice.isInBox) {
        await invoice.delete();
      }

      // Reload invoices to update the UI
      loadInvoices();

      // Update cashbox data immediately
      if (_cashboxCubit != null) {
        _cashboxCubit!.loadCashboxData();
      }

      // Update customer total paid
      if (_customersCubit != null) {
        _customersCubit!.updateCustomerTotalPaid();
      }
    } catch (e) {
      // Error handling without print
      // Continue with updates even if there was an error
      loadInvoices();
      _cashboxCubit?.loadCashboxData();
      _customersCubit?.updateCustomerTotalPaid();
    }
  }

  String generateInvoiceId() {
    return const Uuid().v4();
  }

  /// Process return of items from an invoice
  Future<void> processInvoiceReturn(
    Invoice invoice,
    Map<InvoiceItem, int> returnQuantities,
  ) async {
    try {
      double totalReturnAmount = 0.0;
      final returnItems = <ReturnItem>[];

      // Process each returned item
      for (final entry in returnQuantities.entries) {
        final item = entry.key;
        final returnQty = entry.value;

        if (returnQty > 0 &&
            returnQty <= (item.quantity - item.returnedQuantity)) {
          // Calculate return amount for this item
          final pricePerUnit = item.totalPrice / item.quantity;
          final returnAmount = pricePerUnit * returnQty;

          // Create return item record
          final returnItem = ReturnItem(
            productId: item.product.key?.toString() ?? item.product.category,
            productCategory: item.product.category,
            returnedQuantity: returnQty,
            pricePerUnit: pricePerUnit,
            returnedAmount: returnAmount,
          );
          returnItems.add(returnItem);

          // Update item return quantities
          item.returnedQuantity += returnQty;
          item.returnedAmount += returnAmount;

          // Add to total return amount
          totalReturnAmount += returnAmount;

          // Return products to inventory
          final productInBox = _productsBox.values.firstWhere(
            (p) => p.category == item.product.category,
            orElse: () => item.product,
          );

          productInBox.quantity += returnQty;

          // Save the updated product if it's in a box
          if (productInBox.isInBox) {
            await productInBox.save();
          }

          // Save the updated item if it's in a box
          if (item.isInBox) {
            await item.save();
          }
        }
      }

      // Create return record
      if (returnItems.isNotEmpty) {
        final returnRecord = Return(
          invoiceId: invoice.id,
          returnDate: DateTime.now(),
          items: returnItems,
          totalReturnAmount: totalReturnAmount,
          notes: 'Return processed via app',
        );

        // Save return record to Hive
        if (_returnsCubit != null) {
          await _returnsCubit!.addReturn(returnRecord);
        }
      }

      // Update invoice return totals and subtract from total amount
      invoice.totalReturnedAmount += totalReturnAmount;
      invoice.totalAmount -=
          totalReturnAmount; // Subtract return amount from total
      invoice.lastReturnDate = DateTime.now();
      invoice.hasReturns = invoice.totalReturnedAmount > 0;

      // Save the updated invoice if it's in a box
      if (invoice.isInBox) {
        await invoice.save();
      }

      // Reload data to update UI
      loadInvoices();

      // Update related cubits
      _cashboxCubit?.updateCashbox();
      _productsCubit?.updateProducts();
      _customersCubit?.updateCustomerTotalPaid();
    } catch (e) {
      // Error handling
      debugPrint('Error processing invoice return: $e');
      rethrow;
    }
  }

  // Process a payment from a customer
  Future<void> processCustomerPayment(Customer customer, double amount) async {
    try {
      // Find unpaid invoices for this customer
      final customerInvoices = _invoicesBox.values
          .where((invoice) =>
              invoice.customer.name == customer.name &&
              (!invoice.isPaid ||
                  (invoice.paidAmount ?? 0) < invoice.totalAmount))
          .toList();

      // Sort by date (oldest first)
      customerInvoices.sort((a, b) => a.date.compareTo(b.date));

      double remainingAmount = amount;

      // Apply payment to invoices
      for (final invoice in customerInvoices) {
        if (remainingAmount <= 0) break;

        final unpaidAmount = invoice.totalAmount - (invoice.paidAmount ?? 0);

        if (unpaidAmount > 0) {
          final paymentForThisInvoice =
              remainingAmount > unpaidAmount ? unpaidAmount : remainingAmount;

          // Create payment record for this payment
          if (_paymentRecordsCubit != null) {
            final paymentRecord = PaymentRecord(
              customerId: customer.name,
              invoiceId: invoice.id,
              amount: paymentForThisInvoice,
            );
            await _paymentRecordsCubit!.addPaymentRecord(paymentRecord);
          }

          // Update invoice
          invoice.paidAmount =
              (invoice.paidAmount ?? 0) + paymentForThisInvoice;
          invoice.isPaid = invoice.paidAmount! >= invoice.totalAmount;
          await invoice.save();

          // Reduce remaining amount
          remainingAmount -= paymentForThisInvoice;
        }
      }

      // If there's still remaining amount after paying all invoices,
      // this becomes a credit balance for the customer
      // Create a payment record for the excess amount
      if (remainingAmount > 0 && _paymentRecordsCubit != null) {
        final excessPaymentRecord = PaymentRecord(
          customerId: customer.name,
          invoiceId: 'EXCESS_PAYMENT', // Special identifier for excess payments
          amount: remainingAmount,
        );
        await _paymentRecordsCubit!.addPaymentRecord(excessPaymentRecord);
      }

      // Update state
      loadInvoices();

      // Update related data
      if (_cashboxCubit != null) {
        _cashboxCubit!.loadCashboxData();
      }

      if (_customersCubit != null) {
        _customersCubit!.updateCustomerTotalPaid();
      }
    } catch (e) {
      // Handle errors
      debugPrint('Error processing customer payment: $e');
      rethrow;
    }
  }
}
