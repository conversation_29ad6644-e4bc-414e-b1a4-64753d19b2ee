import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'payment_record.g.dart';

@HiveType(typeId: 20)
class PaymentRecord extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String customerId;

  @HiveField(2)
  String invoiceId;

  @HiveField(3)
  double amount;

  @HiveField(4)
  DateTime timestamp;

  @HiveField(5)
  late int timestampSeconds;

  @HiveField(6)
  late int timestampMinutes;

  @HiveField(7)
  late int timestampDays;

  PaymentRecord({
    String? id,
    required this.customerId,
    required this.invoiceId,
    required this.amount,
    DateTime? timestamp,
    int? timestampSeconds,
    int? timestampMinutes,
    int? timestampDays,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now() {
    // Calculate timestamp components if not provided
    final now = this.timestamp;
    this.timestampSeconds = timestampSeconds ?? now.second;
    this.timestampMinutes = timestampMinutes ?? now.minute;
    this.timestampDays = timestampDays ?? now.day;
  }

  // Helper getters for formatted display
  String get formattedTimestamp {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
  }

  String get detailedTimestamp {
    return 'اليوم: $timestampDays، الدقيقة: $timestampMinutes، الثانية: $timestampSeconds';
  }
}
