import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/invoice.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

class PaymentDialog extends StatefulWidget {
  final Invoice invoice;
  final Function(double) onPaymentComplete;

  const PaymentDialog({
    super.key,
    required this.invoice,
    required this.onPaymentComplete,
  });

  @override
  State<PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<PaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.invoice.totalAmount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.0),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Header with icon
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue,
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: const Icon(
                Icons.payment,
                size: 32,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              localizations.paymentVoucher,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Customer info
            Text(
              'رقم الواتساب: ${widget.invoice.customer.whatsappNumber}',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.neutralGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Total amount display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppColors.warningOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: AppColors.warningOrange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Text(
                    'الرصيد المتبقي:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.neutralGrey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'SAR ${widget.invoice.totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.warningOrange,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Payment amount input
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppColors.successGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: AppColors.successGreen.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Text(
                    'سند الدفع:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.neutralGrey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Form(
                    key: _formKey,
                    child: TextFormField(
                      controller: _amountController,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.successGreen,
                      ),
                      decoration: InputDecoration(
                        hintText: '0.00',
                        hintStyle: TextStyle(
                          color: AppColors.successGreen.withValues(alpha: 0.5),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 16.0,
                        ),
                        prefixText: 'SAR ',
                        prefixStyle: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.successGreen,
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterAmount;
                        }
                        final amount = double.tryParse(value);
                        if (amount == null) {
                          return localizations.invalidAmount;
                        }
                        if (amount > widget.invoice.totalAmount) {
                          return localizations.amountExceedsTotal;
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ),

            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.errorRed.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(
                      color: AppColors.errorRed,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            const SizedBox(height: 32),

            // Single centered pay button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _handlePayment,
                icon: const Icon(Icons.payment, size: 20),
                label: Text(
                  localizations.pay,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 16.0,
                  ),
                  elevation: 4,
                  shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Cancel text button (subtle)
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.neutralGrey,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handlePayment() {
    // Check if _formKey.currentState is not null before calling validate
    if (_formKey.currentState != null && _formKey.currentState!.validate()) {
      try {
        final amount = double.parse(_amountController.text);
        widget.onPaymentComplete(amount);
        Navigator.pop(context);
      } catch (e) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }
}

void showPaymentDialog({
  required BuildContext context,
  required Invoice invoice,
  required Function(double) onPaymentComplete,
}) {
  showDialog(
    context: context,
    builder: (context) => PaymentDialog(
      invoice: invoice,
      onPaymentComplete: onPaymentComplete,
    ),
  );
}
