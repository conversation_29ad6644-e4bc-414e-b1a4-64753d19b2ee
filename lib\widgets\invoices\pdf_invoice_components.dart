import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../models/invoice.dart';
import '../../l10n/app_localizations.dart';

// Header component
pw.Widget buildHeader(pw.Font ttf, pw.MemoryImage logo, pw.MemoryImage qrCode,
    AppLocalizations localizations) {
  return pw.Stack(
    alignment: pw.Alignment.topCenter,
    children: [
      pw.Container(
        width: double.infinity,
        height: 30,
        color: PdfColor.fromHex('#125158'), // Dark gray
      ),
      pw.Container(
        width: 300,
        height: 50,
        color: PdfColor.fromHex('#1c666d'), // Dark gray
      ),
    ],
  );
}

// Invoice title component

// Invoice details component
pw.Widget buildInvoiceDetails(
    Invoice invoice, pw.Font ttf, pw.MemoryImage logo) {
  String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  return pw.Column(
    children: [
      pw.SizedBox(height: 5),
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.SizedBox(
            height: 80,
            width: 80,
            child: pw.Image(logo, fit: pw.BoxFit.fill),
          ),
          pw.SizedBox(width: 5),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              pw.Text(
                'مؤسسه محمد علي بكري البيطريه',
                style: pw.TextStyle(
                  font: ttf,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 18,
                  color: PdfColor.fromHex('#125158'),
                ),
              ),
              pw.Text(
                'س.ت 4031319572',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 15,
                  color: PdfColor.fromHex('#125158'), // Dark gray
                ),
              ),
            ],
          ),
        ],
      ),
      pw.SizedBox(height: 10),
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(
                children: [
                  pw.Text(
                    'رقم الفاتورة: ',
                    style: pw.TextStyle(
                      font: ttf,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                      color: PdfColor.fromHex('#1c666d'), // Dark gray
                    ),
                  ),
                  pw.Text(
                    invoice.id.length > 8
                        ? invoice.id.substring(0, 8)
                        : invoice.id,
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 13,
                      color: PdfColor.fromHex('#000000'), // Dark gray
                    ),
                  ),
                ],
              ),
              // Date row
              pw.Row(
                children: [
                  pw.Text(
                    'تاريخ الفاتورة: ',
                    style: pw.TextStyle(
                      font: ttf,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                      color: PdfColor.fromHex('#1c666d'), // Dark gray
                    ),
                  ),
                  pw.Text(
                    formatDate(invoice.date),
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 13,
                      color: PdfColor.fromHex('#000000'), // Dark gray
                    ),
                  ),
                ],
              ),

              pw.SizedBox(width: 10),
              pw.Row(
                children: [
                  pw.Text(
                    'اسم العميل: ',
                    style: pw.TextStyle(
                      font: ttf,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                      color: PdfColor.fromHex('#1c666d'), // Dark gray
                    ),
                  ),
                  pw.Text(
                    invoice.customer.name,
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 13,
                      color: PdfColor.fromHex('#000000'), // Dark gray
                    ),
                  ),
                ],
              ),
            ],
          ),
          pw.Text(
            'فاتورة مبيعات      ',
            style: pw.TextStyle(
              font: ttf,
              fontWeight: pw.FontWeight.bold,
              fontSize: 36,
              color: PdfColor.fromHex('#125158'),
            ),
          ),
        ],
      ),
    ],
  );
}
