import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/invoice.dart';
import '../models/customer.dart';
import '../models/return.dart';

import '../cubits/returns_cubit.dart';
import '../cubits/payment_records_cubit.dart';
import '../l10n/app_localizations.dart';
import '../widgets/pdf/customer_statement_generator.dart';
import '../constants/app_colors.dart';

class CustomerSummaryPage extends StatelessWidget {
  final Customer customer;
  final List<Invoice> invoices;

  const CustomerSummaryPage({
    super.key,
    required this.customer,
    required this.invoices,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final returnsCubit = context.read<ReturnsCubit>();

    // Calculate summary statistics
    final totalInvoices = invoices.length;
    final totalInvoiceAmount = invoices.fold<double>(
      0.0,
      (sum, invoice) =>
          sum + (invoice.totalAmount + invoice.totalReturnedAmount),
    );
    final totalPaid = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + (invoice.paidAmount ?? 0.0),
    );
    final totalReturned = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalReturnedAmount,
    );
    final currentTotalAmount = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final totalRemaining = currentTotalAmount - totalPaid;

    return Scaffold(
      backgroundColor: AppColors.backgroundBlue,
      appBar: AppBar(
        title: Text(
          localizations.customerSummary,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            tooltip: 'مشاركة كشف الحساب',
            onPressed: () => _shareCustomerStatement(context, localizations),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    AppColors.primaryBlue,
                    AppColors.accentCyan,
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.customerSummary,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              customer.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Summary Statistics
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.financialSummary,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Summary rows
                  _buildSummaryRow(
                    localizations.totalInvoices,
                    totalInvoices.toString(),
                    Icons.receipt_long,
                    AppColors.primaryBlue,
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    localizations.totalInvoiceAmount,
                    'SAR ${totalInvoiceAmount.toStringAsFixed(2)}',
                    Icons.account_balance_wallet,
                    AppColors.accentIndigo,
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    localizations.totalReturned,
                    'SAR ${totalReturned.toStringAsFixed(2)}',
                    Icons.keyboard_return,
                    AppColors.warningOrange,
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    localizations.netAmount,
                    'SAR ${currentTotalAmount.toStringAsFixed(2)}',
                    Icons.calculate,
                    AppColors.accentTeal,
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    localizations.totalPaidByCustomer,
                    'SAR ${totalPaid.toStringAsFixed(2)}',
                    Icons.payment,
                    AppColors.successGreen,
                  ),
                  const SizedBox(height: 12),
                  _buildSummaryRow(
                    totalRemaining < 0
                        ? 'رصيد العميل'
                        : localizations.totalRemainingAmount,
                    totalRemaining < 0
                        ? 'SAR ${(-totalRemaining).toStringAsFixed(2)}'
                        : 'SAR ${totalRemaining.toStringAsFixed(2)}',
                    totalRemaining < 0
                        ? Icons.account_balance
                        : Icons.pending_actions,
                    totalRemaining > 0
                        ? AppColors.errorRed
                        : AppColors.successGreen,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Invoice Details
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.invoices,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Invoice list
                  ...invoices.map((invoice) {
                    final returnRecords =
                        returnsCubit.getReturnsForInvoice(invoice.id);
                    return _buildInvoiceCard(
                        context, invoice, returnRecords, localizations);
                  }).toList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
      String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceCard(
    BuildContext context,
    Invoice invoice,
    List<Return> returnRecords,
    AppLocalizations localizations,
  ) {
    final originalAmount = invoice.totalAmount + invoice.totalReturnedAmount;
    final paidAmount = invoice.paidAmount ?? 0.0;
    final remainingAmount = invoice.totalAmount - paidAmount;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundBlue.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Invoice header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.receipt_long,
                  color: AppColors.primaryBlue,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '${localizations.invoiceNumber}: #${invoice.id.length > 8 ? invoice.id.substring(0, 8) : invoice.id}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryBlue,
                          ),
                        ),
                        if (invoice.hasReturns) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.warningOrange
                                  .withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              localizations.hasReturns,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: AppColors.warningOrange,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      '${localizations.invoiceDate}: ${_formatDate(invoice.date)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.neutralGrey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Financial details
          if (invoice.hasReturns) ...[
            _buildInvoiceDetailRow(
              localizations.originalAmount,
              'SAR ${originalAmount.toStringAsFixed(2)}',
              Colors.grey[600]!,
              hasStrikethrough: true,
            ),
            const SizedBox(height: 4),
            _buildInvoiceDetailRow(
              localizations.totalReturned,
              '- SAR ${invoice.totalReturnedAmount.toStringAsFixed(2)}',
              AppColors.warningOrange,
            ),
            const SizedBox(height: 4),
            const Divider(height: 8),
          ],

          _buildInvoiceDetailRow(
            localizations.invoiceAmount,
            'SAR ${invoice.totalAmount.toStringAsFixed(2)}',
            AppColors.primaryBlue,
            isBold: true,
          ),
          const SizedBox(height: 4),
          _buildInvoiceDetailRow(
            localizations.paidForInvoice,
            'SAR ${paidAmount.toStringAsFixed(2)}',
            AppColors.successGreen,
          ),
          const SizedBox(height: 4),
          _buildInvoiceDetailRow(
            localizations.remainingForInvoice,
            'SAR ${remainingAmount.toStringAsFixed(2)}',
            remainingAmount > 0 ? AppColors.errorRed : AppColors.successGreen,
            isBold: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceDetailRow(
    String label,
    String value,
    Color color, {
    bool isBold = false,
    bool hasStrikethrough = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: color,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: color,
            decoration: hasStrikethrough ? TextDecoration.lineThrough : null,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _shareCustomerStatement(
      BuildContext context, AppLocalizations localizations) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Get payment records for this customer
      final paymentRecordsCubit = context.read<PaymentRecordsCubit>();
      final paymentRecords =
          paymentRecordsCubit.getPaymentRecordsForCustomer(customer.name);

      // Generate and share the PDF
      await CustomerStatementGenerator.generateAndShareStatement(
        context,
        customer,
        invoices,
        paymentRecords,
      );

      // Close loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Close loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء كشف الحساب: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }
}
