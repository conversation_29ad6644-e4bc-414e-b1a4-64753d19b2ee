import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/customer.dart';
import '../../models/invoice.dart';
import '../../cubits/customers_cubit.dart';
import '../../cubits/invoices_cubit.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

class CustomerPaymentDialog extends StatefulWidget {
  final Customer customer;

  const CustomerPaymentDialog({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerPaymentDialog> createState() => _CustomerPaymentDialogState();
}

class _CustomerPaymentDialogState extends State<CustomerPaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  String? _errorMessage;
  bool _showOverpaymentWarning = false;

  @override
  void initState() {
    super.initState();
    // Initialize with the remaining balance amount
    if (widget.customer.remainingBalance > 0) {
      _amountController.text =
          widget.customer.remainingBalance.toStringAsFixed(2);
    } else {
      _amountController.text = "0.00";
    }
    _amountController.addListener(_checkOverpayment);
  }

  void _checkOverpayment() {
    final amount = double.tryParse(_amountController.text);
    if (amount != null && amount > widget.customer.remainingBalance) {
      if (!_showOverpaymentWarning) {
        setState(() {
          _showOverpaymentWarning = true;
        });
      }
    } else {
      if (_showOverpaymentWarning) {
        setState(() {
          _showOverpaymentWarning = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.0),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Header with icon
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppColors.backgroundBlue,
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: const Icon(
                Icons.payment,
                size: 32,
                color: AppColors.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              localizations.paymentVoucher,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppColors.darkGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Customer info
            Text(
              'رقم الواتساب: ${widget.customer.whatsappNumber}',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.neutralGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Customer balance display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: widget.customer.remainingBalance < 0
                    ? AppColors.successGreen.withValues(alpha: 0.1)
                    : AppColors.warningOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: widget.customer.remainingBalance < 0
                      ? AppColors.successGreen.withValues(alpha: 0.3)
                      : AppColors.warningOrange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    widget.customer.remainingBalance < 0
                        ? 'رصيد العميل:'
                        : 'الرصيد المتبقي:',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.neutralGrey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.customer.remainingBalance < 0
                        ? 'SAR ${(-widget.customer.remainingBalance).toStringAsFixed(2)}'
                        : 'SAR ${widget.customer.remainingBalance.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: widget.customer.remainingBalance < 0
                          ? AppColors.successGreen
                          : AppColors.warningOrange,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Payment amount input
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: AppColors.successGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: AppColors.successGreen.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Text(
                    'سند الدفع:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.neutralGrey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Form(
                    key: _formKey,
                    child: TextFormField(
                      controller: _amountController,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.successGreen,
                      ),
                      decoration: InputDecoration(
                        hintText: '0.00',
                        hintStyle: TextStyle(
                          color: AppColors.successGreen.withValues(alpha: 0.5),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 16.0,
                        ),
                        prefixText: 'SAR ',
                        prefixStyle: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.successGreen,
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.pleaseEnterAmount;
                        }
                        final amount = double.tryParse(value);
                        if (amount == null) {
                          return localizations.invalidAmount;
                        }
                        if (amount <= 0) {
                          return 'يرجى إدخال مبلغ أكبر من الصفر';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ),

            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.errorRed.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(
                      color: AppColors.errorRed,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            if (_showOverpaymentWarning)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.warningOrange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.warningOrange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.warning_amber,
                        color: AppColors.warningOrange,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'دفع زائد',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppColors.warningOrange,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              'المبلغ أكبر من المطلوب. سيصبح للعميل رصيد دائن.',
                              style: TextStyle(
                                color: AppColors.warningOrange
                                    .withValues(alpha: 0.8),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 32),

            // Single centered pay button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _handlePayment,
                icon: const Icon(Icons.payment, size: 20),
                label: Text(
                  localizations.pay,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32.0,
                    vertical: 16.0,
                  ),
                  elevation: 4,
                  shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Cancel text button (subtle)
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.neutralGrey,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handlePayment() async {
    if (_formKey.currentState!.validate()) {
      try {
        final amount = double.parse(_amountController.text);

        // Get the customer's unpaid invoices
        final invoicesCubit = context.read<InvoicesCubit>();
        final customersCubit = context.read<CustomersCubit>();
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        final navigator = Navigator.of(context);
        final localizations = AppLocalizations.of(context);

        // Create a payment record or update invoices as needed
        await invoicesCubit.processCustomerPayment(widget.customer, amount);

        // Update customer data and wait for it to complete
        customersCubit.updateCustomerTotalPaid();

        // Close the dialog
        navigator.pop();

        // Show success message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(localizations.paymentSuccessful),
            backgroundColor: AppColors.successGreen,
          ),
        );
      } catch (e) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }
}

void showCustomerPaymentDialog({
  required BuildContext context,
  required Customer customer,
}) {
  showDialog(
    context: context,
    builder: (context) => CustomerPaymentDialog(
      customer: customer,
    ),
  );
}
