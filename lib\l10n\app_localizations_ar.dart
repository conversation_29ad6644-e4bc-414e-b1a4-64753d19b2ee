import 'app_localizations.dart';

class AppLocalizationsAr extends AppLocalizations {
  // General
  @override
  String get appTitle => 'عيادة الزبيدي البيطرية';

  // Navigation
  @override
  String get productsTab => 'المنتجات';
  @override
  String get accountsTab => 'الحسابات';
  @override
  String get invoicesTab => 'الفواتير';
  @override
  String get cashboxTab => 'الصندوق';

  // Products Page
  @override
  String get lowStock => 'المخزون على وشك النفاد';
  @override
  String get expiringSoon => 'تحذير: ستنتهي الصلاحية قريبًا';
  @override
  String get allProducts => 'جميع المنتجات';
  @override
  String get quantity => 'الكمية';
  @override
  String get price => 'سعر البيع';
  @override
  String get expires => 'تاريخ الانتهاء';
  @override
  String get addProduct => 'إضافة منتج';
  @override
  String get editProduct => 'تعديل المنتج';
  @override
  String get categoryType => 'الفئة/النوع';
  @override
  String get pricePerUnit => 'سعر الوحده للبيع';
  @override
  String get purchasePrice => 'سعر الوحده للشراء';
  @override
  String get expirationDate => 'تاريخ انتهاء الصلاحية';
  @override
  String get pleaseEnterCategory => 'الرجاء إدخال الفئة';
  @override
  String get pleaseEnterQuantity => 'الرجاء إدخال الكمية';
  @override
  String get pleaseEnterValidQuantity => 'الرجاء إدخال كمية صالحة';
  @override
  String get pleaseEnterPrice => 'الرجاء إدخال السعر';
  @override
  String get pleaseEnterValidPrice => 'الرجاء إدخال سعر صالح';
  @override
  String get pleaseEnterPurchasePrice => 'الرجاء إدخال سعر الشراء';
  @override
  String get pleaseEnterValidPurchasePrice => 'الرجاء إدخال سعر شراء صالح';
  @override
  String get showAll => 'عرض الكل';

  // Consumables
  @override
  String get consumables => 'مصاريف';
  @override
  String get addConsumable => 'إضافة مصاريف';
  @override
  String get editConsumable => 'تعديل مصاريف';
  @override
  String get deleteConsumable => 'حذف مصاريف';
  @override
  String get consumableName => 'اسم المصاريف';
  @override
  String get consumablePrice => 'سعر المصاريف';
  @override
  String get totalConsumables => 'إجمالي المصاريف';
  @override
  String get pleaseEnterConsumableName => 'الرجاء إدخال اسم المصاريف';
  @override
  String get pleaseEnterConsumablePrice => 'الرجاء إدخال سعر المصاريف';
  @override
  String get pleaseEnterValidConsumablePrice =>
      'الرجاء إدخال سعر صالح للمصاريف';
  @override
  String get confirmDeleteConsumable => 'هل أنت متأكد من حذف هذا المصروف؟';
  @override
  String get consumableDeletedSuccessfully => 'تم حذف المصاريف بنجاح';
  @override
  String get consumableAddedSuccessfully => 'تم إضافة المصاريف بنجاح';
  @override
  String get consumableUpdatedSuccessfully => 'تم تحديث المصاريف بنجاح';
  @override
  String get noConsumablesFound => 'لا توجد مصاريف';
  @override
  String get addFirstConsumable => 'أضف أول مصروف';

  // Invoice Preview
  @override
  String get invoicePreview => 'معاينة الفاتورة';
  @override
  String get editInvoice => 'تعديل الفاتورة';
  @override
  String get removeProduct => 'إزالة المنتج';
  @override
  String get addPayment => 'إضافة دفعة';
  @override
  String get finalizeInvoice => 'إنشاء فاتورة';
  @override
  String get invoiceCreatedSuccessfully => 'تم إنشاء الفاتورة بنجاح';
  @override
  String get totalAmount => 'المبلغ الإجمالي';
  @override
  String get paidAmount => 'المبلغ المدفوع';
  @override
  String get pleaseEnterValidAmount => 'يرجى إدخال مبلغ صحيح';
  @override
  String get insufficientStock => 'مخزون غير كافي لـ';
  @override
  String get availableStock => 'المخزون المتاح';

  // Customers
  @override
  String get addCustomer => 'إضافة عميل';
  @override
  String get customerName => 'اسم العميل';
  @override
  String get whatsappNumber => 'رقم الواتساب';
  @override
  String get pleaseEnterName => 'الرجاء إدخال الاسم';
  @override
  String get pleaseEnterWhatsapp => 'الرجاء إدخال رقم الواتساب';
  @override
  String get customerDetails => 'تفاصيل العميل';
  @override
  String get totalPaid => 'المبلغ المدفوع';
  @override
  String get remainingBalance => 'الرصيد المتبقي';
  @override
  String get paymentVoucher => 'سند الدفع';

  // Invoices
  @override
  String get newInvoice => 'فاتورة جديدة';
  @override
  String get customer => 'العميل';
  @override
  String get selectCustomer => 'الرجاء اختيار عميل';
  @override
  String get selectedProducts => 'المنتجات المختارة';
  @override
  String get total => 'المجموع';
  @override
  String get createInvoice => 'إنشاء فاتورة';
  @override
  String get selectProducts => 'اختر المنتجات';
  @override
  String get noProductsAvailable => 'لا توجد منتجات متاحة';
  @override
  String get selectMultipleProducts => 'اختر منتجات متعددة وحدد الكميات';
  @override
  String get addSelected => 'إضافة المحدد';
  @override
  String get invoices => 'الفواتير';
  @override
  String get allInvoices => 'جميع الفواتير';
  @override
  String get customerInvoices => 'فواتير العملاء';
  @override
  String get date => 'التاريخ';
  @override
  String get items => 'العناصر';
  @override
  String get product => 'المنتج';
  @override
  String get close => 'إغلاق';
  @override
  String get share => 'مشاركة';
  @override
  String get taxNumber => 'الرقم الضريبي';
  @override
  String get commercialRegistration => 'السجل التجاري';
  @override
  String get institutionName => 'مؤسسة محمد علي بكري الزبيدي البيطرية';

  // Buttons
  @override
  String get cancel => 'إلغاء';
  @override
  String get save => 'حفظ';
  @override
  String get delete => 'حذف';
  @override
  String get add => 'إضافة';
  @override
  String get refreshData => 'تحديث البيانات';

  // Error messages
  @override
  String get errorAddingProduct => 'حدث خطأ أثناء إضافة المنتج';
  @override
  String get errorUpdatingProduct => 'حدث خطأ أثناء تحديث المنتج';
  @override
  String get errorDeletingProduct => 'حدث خطأ أثناء حذف المنتج';

  // Cashbox Page
  @override
  String get financialSummary => 'ملخص مالي';
  @override
  String get lastUpdated => 'آخر تحديث';
  @override
  String get sales => 'المبيعات';
  @override
  String get purchases => 'أجمالي المنتجات لدي';
  @override
  String get totalProfit => 'إجمالي الربح';
  @override
  String get recentTransactions => 'المعاملات الأخيرة';
  @override
  String get noRecentTransactions => 'لا توجد معاملات حديثة';

  // Payment
  @override
  String get amount => 'المبلغ';
  @override
  String get pleaseEnterAmount => 'الرجاء إدخال المبلغ';
  @override
  String get invalidAmount => 'مبلغ غير صالح';
  @override
  String get amountExceedsTotal => 'المبلغ لا يمكن أن يتجاوز المجموع';
  @override
  String get pay => 'دفع';
  @override
  String get paymentSuccessful => 'تم الدفع بنجاح';

  // Search
  @override
  String get searchProducts => 'البحث عن المنتجات...';
  @override
  String get searchResults => 'نتائج البحث';

  // Returns/Refunds
  @override
  String get returnItems => 'إرجاع العناصر';
  @override
  String get returnInvoice => 'إرجاع الفاتورة';
  @override
  String get selectItemsToReturn => 'اختر العناصر للإرجاع';
  @override
  String get returnQuantity => 'كمية الإرجاع';
  @override
  String get returnAmount => 'مبلغ الإرجاع';
  @override
  String get totalReturned => 'إجمالي المرتجع';
  @override
  String get returnSuccessful => 'تم الإرجاع بنجاح';
  @override
  String get returnDialog => 'حوار الإرجاع';
  @override
  String get confirmReturn => 'تأكيد الإرجاع';
  @override
  String get returnNote => 'ملاحظة الإرجاع';
  @override
  String get originalQuantity => 'الكمية الأصلية';
  @override
  String get returnedQuantity => 'الكمية المرتجعة';
  @override
  String get netQuantity => 'الكمية الصافية';
  @override
  String get returnDate => 'تاريخ الإرجاع';
  @override
  String get partialReturn => 'إرجاع جزئي';
  @override
  String get fullReturn => 'إرجاع كامل';
  @override
  String get cannotReturnMoreThanSold => 'لا يمكن إرجاع أكثر من المباع';
  @override
  String get pleaseSelectItemsToReturn => 'الرجاء اختيار عناصر للإرجاع';
  @override
  String get originalAmount => 'المبلغ الأصلي';
  @override
  String get netAmount => 'المبلغ الصافي';

  // Customer Summary
  @override
  String get customerSummary => 'ملخص العميل';
  @override
  String get viewSummary => 'عرض الملخص';
  @override
  String get totalInvoices => 'إجمالي الفواتير';
  @override
  String get totalInvoiceAmount => 'إجمالي مبلغ الفواتير';
  @override
  String get totalPaidByCustomer => 'إجمالي المدفوع من العميل';
  @override
  String get totalRemainingAmount => 'إجمالي المبلغ المتبقي';
  @override
  String get invoiceNumber => 'رقم الفاتورة';
  @override
  String get invoiceDate => 'تاريخ الفاتورة';
  @override
  String get invoiceAmount => 'مبلغ الفاتورة';
  @override
  String get paidForInvoice => 'المدفوع للفاتورة';
  @override
  String get remainingForInvoice => 'المتبقي للفاتورة';
  @override
  String get returnStatus => 'حالة الإرجاع';
  @override
  String get noReturns => 'لا توجد إرجاعات';
  @override
  String get hasReturns => 'يوجد إرجاعات';

  // Collections
  @override
  String get collections => 'المجموعات';
  @override
  String get saveCollection => 'حفظ المجموعة';
  @override
  String get loadCollection => 'تحميل المجموعة';
  @override
  String get collectionName => 'اسم المجموعة';
  @override
  String get enterCollectionName => 'أدخل اسم المجموعة';
  @override
  String get collectionSaved => 'تم حفظ المجموعة بنجاح';
  @override
  String get collectionLoaded => 'تم تحميل المجموعة بنجاح';
  @override
  String get deleteCollection => 'حذف المجموعة';
  @override
  String get confirmDeleteCollection => 'هل أنت متأكد من حذف هذه المجموعة؟';
  @override
  String get noCollections => 'لا توجد مجموعات محفوظة';
  @override
  String get manageCollections => 'إدارة المجموعات';
  @override
  String get search => 'بحث';
}
