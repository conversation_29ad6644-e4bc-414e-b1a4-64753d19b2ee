import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../l10n/app_localizations.dart';

class CustomerStatementGenerator {
  static Future<void> generateAndShareStatement(
    BuildContext context,
    Customer customer,
    List<Invoice> invoices,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load the logo image
    final logoImage = await rootBundle.load('assets/logo.jpg');
    final logoImageData = logoImage.buffer.asUint8List();
    final logo = pw.MemoryImage(logoImageData);

    // Create a theme with the Arabic font
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Sort invoices by date (oldest first) for proper sequential numbering
    final sortedInvoices = List<Invoice>.from(invoices);
    sortedInvoices.sort((a, b) => a.date.compareTo(b.date));

    // Calculate summary data using sorted invoices
    final totalInvoices = sortedInvoices.length;
    final totalInvoiceAmount = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) =>
          sum + (invoice.totalAmount + invoice.totalReturnedAmount),
    );
    final totalPaid = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + (invoice.paidAmount ?? 0.0),
    );
    final totalReturned = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalReturnedAmount,
    );
    final currentTotalAmount = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final totalRemaining = currentTotalAmount - totalPaid;

    // Add page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        margin: const pw.EdgeInsets.all(20),
        maxPages: 100,
        header: (context) => _buildHeader(logo, ttf, localizations),
        footer: (context) => _buildFooter(context, ttf, localizations),
        build: (context) => [
          _buildStatementHeader(customer, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildCustomerInfo(customer, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildSummarySection(
            totalInvoices,
            totalInvoiceAmount,
            totalPaid,
            totalReturned,
            currentTotalAmount,
            totalRemaining,
            ttf,
            localizations,
          ),
          pw.SizedBox(height: 20),
          _buildInvoicesTable(sortedInvoices, ttf, localizations),
        ],
      ),
    );

    // Save and share the PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/customer_statement_${customer.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '${localizations.customerSummary} - ${customer.name}',
    );
  }

  static pw.Widget _buildHeader(
    pw.MemoryImage logo,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColors.blue,
            width: 2,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'مؤسسة محمد علي بكري الزبيدي البيطرية',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'كشف حساب العميل',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.Container(
            width: 60,
            height: 60,
            child: pw.Image(logo),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(
    pw.Context context,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColors.blue,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تاريخ الطباعة: ${_formatDate(DateTime.now())}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'صفحة ${context.pageNumber}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildStatementHeader(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#1976D2'),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'كشف حساب العميل',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'تاريخ الإصدار: ${_formatDate(DateTime.now())}',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: pw.BoxDecoration(
              color: PdfColors.white,
              borderRadius: pw.BorderRadius.circular(4),
            ),
            child: pw.Text(
              'رقم العميل: ${customer.whatsappNumber}',
              style: pw.TextStyle(
                font: ttf,
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColor.fromHex('#1976D2'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCustomerInfo(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F8F9FA'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColor.fromHex('#DEE2E6'),
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'بيانات العميل',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#495057'),
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            children: [
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'اسم العميل',
                  customer.name,
                  ttf,
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'رقم الواتساب',
                  customer.whatsappNumber,
                  ttf,
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'إجمالي المدفوع',
                  '${customer.totalPaid.toStringAsFixed(2)} ريال',
                  ttf,
                  valueColor: PdfColor.fromHex('#28A745'),
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'الرصيد المتبقي',
                  '${customer.remainingBalance.toStringAsFixed(2)} ريال',
                  ttf,
                  valueColor: customer.remainingBalance > 0
                      ? PdfColor.fromHex('#DC3545')
                      : PdfColor.fromHex('#28A745'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInfoItem(
    String label,
    String value,
    pw.Font ttf, {
    PdfColor? valueColor,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 10,
            color: PdfColor.fromHex('#6C757D'),
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: valueColor ?? PdfColor.fromHex('#212529'),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildSummarySection(
    int totalInvoices,
    double totalInvoiceAmount,
    double totalPaid,
    double totalReturned,
    double currentTotalAmount,
    double totalRemaining,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            PdfColor.fromHex('#E3F2FD'),
            PdfColor.fromHex('#F8F9FA'),
          ],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(
          color: PdfColor.fromHex('#1976D2'),
          width: 2,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('#1976D2'),
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Text(
              'الملخص المالي للحساب',
              style: pw.TextStyle(
                font: ttf,
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.white,
              ),
            ),
          ),
          pw.SizedBox(height: 16),
          // First row - Basic info
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'عدد الفواتير',
                  totalInvoices.toString(),
                  'فاتورة',
                  ttf,
                  PdfColor.fromHex('#6C757D'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي قيمة الفواتير',
                  totalInvoiceAmount.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#17A2B8'),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          // Second row - Returns and net
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي المرتجعات',
                  totalReturned.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#FFC107'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'صافي المبلغ المستحق',
                  currentTotalAmount.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#6F42C1'),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          // Third row - Payments and remaining
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي المدفوع',
                  totalPaid.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#28A745'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  totalRemaining > 0 ? 'المبلغ المتبقي' : 'رصيد زائد للعميل',
                  totalRemaining.abs().toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  totalRemaining > 0
                      ? PdfColor.fromHex('#DC3545')
                      : PdfColor.fromHex('#28A745'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildEnhancedSummaryItem(
    String label,
    String value,
    String unit,
    pw.Font ttf,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: color,
          width: 1.5,
        ),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColor.fromHex('#00000010'),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColor.fromHex('#6C757D'),
            ),
          ),
          pw.SizedBox(height: 6),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                value,
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: color,
                ),
              ),
              pw.SizedBox(width: 4),
              pw.Text(
                unit,
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 9,
                  color: PdfColor.fromHex('#6C757D'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoicesTable(
    List<Invoice> invoices,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تفاصيل الفواتير',
          style: pw.TextStyle(
            font: ttf,
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue,
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(
            color: PdfColors.grey400,
            width: 0.5,
          ),
          columnWidths: {
            0: const pw.FlexColumnWidth(1.5), // Sequential Number
            1: const pw.FlexColumnWidth(2.5), // Date
            2: const pw.FlexColumnWidth(2.5), // Invoice ID
            3: const pw.FlexColumnWidth(2), // Original Amount
            4: const pw.FlexColumnWidth(2), // Returned
            5: const pw.FlexColumnWidth(2), // Net Amount
            6: const pw.FlexColumnWidth(2), // Paid
            7: const pw.FlexColumnWidth(2), // Remaining
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(
                color: PdfColors.blue,
              ),
              children: [
                _buildTableCell('م', ttf, isHeader: true),
                _buildTableCell('التاريخ', ttf, isHeader: true),
                _buildTableCell('رقم الفاتورة', ttf, isHeader: true),
                _buildTableCell('المبلغ الأصلي', ttf, isHeader: true),
                _buildTableCell('المرتجع', ttf, isHeader: true),
                _buildTableCell('صافي المبلغ', ttf, isHeader: true),
                _buildTableCell('المدفوع', ttf, isHeader: true),
                _buildTableCell('المتبقي', ttf, isHeader: true),
              ],
            ),
            // Data rows with sequential numbering
            ...invoices.asMap().entries.map((entry) {
              final index = entry.key;
              final invoice = entry.value;
              final sequentialNumber = index + 1; // Start from 1
              final originalAmount =
                  invoice.totalAmount + invoice.totalReturnedAmount;
              final paidAmount = invoice.paidAmount ?? 0.0;
              final remainingAmount = invoice.totalAmount - paidAmount;

              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: index % 2 == 0
                      ? PdfColors.white
                      : PdfColor.fromHex('#F9F9F9'),
                ),
                children: [
                  _buildTableCell(sequentialNumber.toString(), ttf),
                  _buildTableCell(_formatDateShort(invoice.date), ttf),
                  _buildTableCell(invoice.id, ttf),
                  _buildTableCell(
                    '${originalAmount.toStringAsFixed(2)} ريال',
                    ttf,
                  ),
                  _buildTableCell(
                    '${invoice.totalReturnedAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: invoice.totalReturnedAmount > 0
                        ? PdfColor.fromHex('#FF9800')
                        : null,
                  ),
                  _buildTableCell(
                    '${invoice.totalAmount.toStringAsFixed(2)} ريال',
                    ttf,
                  ),
                  _buildTableCell(
                    '${paidAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: paidAmount > 0 ? PdfColor.fromHex('#4CAF50') : null,
                  ),
                  _buildTableCell(
                    '${remainingAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: remainingAmount > 0
                        ? PdfColor.fromHex('#F44336')
                        : PdfColor.fromHex('#4CAF50'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(
    String text,
    pw.Font ttf, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.white : (color ?? PdfColors.grey800),
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static String _formatDate(DateTime date) {
    const monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];

    final day = date.day.toString().padLeft(2, '0');
    final month = monthNames[date.month - 1];
    final year = date.year.toString();

    return '$day $month $year';
  }

  static String _formatDateShort(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
