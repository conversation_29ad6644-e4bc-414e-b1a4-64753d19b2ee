import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

abstract class AppLocalizations {
  static const delegate = AppLocalizationsDelegate();
  static AppLocalizations of(BuildContext context) {
    final localizations =
        Localizations.of<AppLocalizations>(context, AppLocalizations);
    if (localizations != null) {
      return localizations;
    }
    // Return a default implementation if localizations are not yet available
    return AppLocalizationsAr();
  }

  static const List<Locale> supportedLocales = [
    Locale('ar'), // Arabic
    Locale('en'), // English
  ];

  // General
  String get appTitle;

  // Navigation
  String get productsTab;
  String get accountsTab;
  String get invoicesTab;
  String get cashboxTab;

  // Products Page
  String get lowStock;
  String get expiringSoon;
  String get allProducts;
  String get quantity;
  String get price;
  String get expires;
  String get addProduct;
  String get editProduct;
  String get categoryType;
  String get pricePerUnit;
  String get purchasePrice;
  String get expirationDate;
  String get pleaseEnterCategory;
  String get pleaseEnterQuantity;
  String get pleaseEnterValidQuantity;
  String get pleaseEnterPrice;
  String get pleaseEnterValidPrice;
  String get pleaseEnterPurchasePrice;
  String get pleaseEnterValidPurchasePrice;
  String get showAll;

  // Consumables
  String get consumables;
  String get addConsumable;
  String get editConsumable;
  String get deleteConsumable;
  String get consumableName;
  String get consumablePrice;
  String get totalConsumables;
  String get pleaseEnterConsumableName;
  String get pleaseEnterConsumablePrice;
  String get pleaseEnterValidConsumablePrice;
  String get confirmDeleteConsumable;
  String get consumableDeletedSuccessfully;
  String get consumableAddedSuccessfully;
  String get consumableUpdatedSuccessfully;
  String get noConsumablesFound;
  String get addFirstConsumable;

  // Invoice Preview
  String get invoicePreview;
  String get editInvoice;
  String get removeProduct;
  String get addPayment;
  String get finalizeInvoice;
  String get invoiceCreatedSuccessfully;
  String get totalAmount;
  String get paidAmount;
  String get pleaseEnterValidAmount;
  String get insufficientStock;
  String get availableStock;

  // Customers
  String get addCustomer;
  String get customerName;
  String get whatsappNumber;
  String get pleaseEnterName;
  String get pleaseEnterWhatsapp;
  String get customerDetails;
  String get totalPaid;
  String get remainingBalance;
  String get paymentVoucher;

  // Invoices
  String get newInvoice;
  String get customer;
  String get selectCustomer;
  String get selectedProducts;
  String get total;
  String get createInvoice;
  String get selectProducts;
  String get noProductsAvailable;
  String get selectMultipleProducts;
  String get addSelected;
  String get invoices;
  String get allInvoices;
  String get customerInvoices;
  String get date;
  String get items;
  String get product;
  String get close;
  String get share;
  String get taxNumber;
  String get commercialRegistration;
  String get institutionName;

  // Buttons
  String get cancel;
  String get save;
  String get delete;
  String get add;
  String get refreshData;

  // Error messages
  String get errorAddingProduct;
  String get errorUpdatingProduct;
  String get errorDeletingProduct;

  // Cashbox Page
  String get financialSummary;
  String get lastUpdated;
  String get sales;
  String get purchases;
  String get totalProfit;
  String get recentTransactions;
  String get noRecentTransactions;

  // Payment
  String get amount;
  String get pleaseEnterAmount;
  String get invalidAmount;
  String get amountExceedsTotal;
  String get pay;
  String get paymentSuccessful;

  // Search
  String get searchProducts;
  String get searchResults;

  // Returns/Refunds
  String get returnItems;
  String get returnInvoice;
  String get selectItemsToReturn;
  String get returnQuantity;
  String get returnAmount;
  String get totalReturned;
  String get returnSuccessful;
  String get returnDialog;
  String get confirmReturn;
  String get returnNote;
  String get originalQuantity;
  String get returnedQuantity;
  String get netQuantity;
  String get returnDate;
  String get partialReturn;
  String get fullReturn;
  String get cannotReturnMoreThanSold;
  String get pleaseSelectItemsToReturn;
  String get originalAmount;
  String get netAmount;

  // Customer Summary
  String get customerSummary;
  String get viewSummary;
  String get totalInvoices;
  String get totalInvoiceAmount;
  String get totalPaidByCustomer;
  String get totalRemainingAmount;
  String get invoiceNumber;
  String get invoiceDate;
  String get invoiceAmount;
  String get paidForInvoice;
  String get remainingForInvoice;
  String get returnStatus;
  String get noReturns;
  String get hasReturns;

  // Collections
  String get collections;
  String get saveCollection;
  String get loadCollection;
  String get collectionName;
  String get enterCollectionName;
  String get collectionSaved;
  String get collectionLoaded;
  String get deleteCollection;
  String get confirmDeleteCollection;
  String get noCollections;
  String get manageCollections;
  String get search;
}

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    Intl.defaultLocale = locale.languageCode;

    switch (locale.languageCode) {
      case 'ar':
        return AppLocalizationsAr();
      case 'en':
      default:
        return AppLocalizationsEn();
    }
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}
